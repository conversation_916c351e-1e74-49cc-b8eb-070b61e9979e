#!/usr/bin/env python3
"""
测试文件跳过行为

验证 main_papers_meta 函数是否正确跳过已存在的文件
"""

import os
import sys
import json
import tempfile
import logging
from pathlib import Path

# 添加项目根目录到路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(ROOT_DIR)

from crawler.dblp_unify.crawler_dblp_unify import DBLPPaperFetcher


def test_file_skip_behavior():
    """测试文件跳过行为"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🧪 测试文件跳过行为")
    print("=" * 50)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 临时测试目录: {temp_dir}")
        
        # 创建 DBLPPaperFetcher 实例
        fetcher = DBLPPaperFetcher(data_dir=temp_dir)
        
        # 测试会议和年份
        test_venue = "aaai"
        test_year = 2021
        
        # 预期的文件路径
        expected_filename = f"{test_venue}_{test_year}.json"
        expected_filepath = os.path.join(temp_dir, expected_filename)
        
        print(f"\n📄 测试会议: {test_venue} {test_year}")
        print(f"📄 预期文件: {expected_filename}")
        
        # 第一次调用 - 应该创建新文件
        print("\n🔄 第一次调用 get_papers_by_venue_and_year...")
        try:
            filepath1 = fetcher.get_papers_by_venue_and_year(test_venue, test_year)
            if filepath1:
                print(f"✅ 第一次调用成功，文件路径: {filepath1}")
                print(f"📊 文件是否存在: {os.path.exists(filepath1)}")
                
                # 读取文件内容以验证
                if os.path.exists(filepath1):
                    with open(filepath1, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"📊 文件中论文数量: {len(data.get('papers', []))}")
                    
                    # 模拟添加摘要数据
                    if data.get('papers'):
                        data['papers'][0]['abstract'] = "这是一个测试摘要，用于验证文件不会被覆盖"
                        data['papers'][0]['abstract_source'] = "test_modification"
                    
                    # 保存修改后的数据
                    with open(filepath1, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    print("✅ 已添加测试摘要数据到第一篇论文")
            else:
                print("❌ 第一次调用失败")
                return
                
        except Exception as e:
            print(f"❌ 第一次调用异常: {e}")
            return
        
        # 第二次调用 - 应该跳过已存在的文件
        print("\n🔄 第二次调用 get_papers_by_venue_and_year...")
        try:
            filepath2 = fetcher.get_papers_by_venue_and_year(test_venue, test_year)
            if filepath2:
                print(f"✅ 第二次调用成功，文件路径: {filepath2}")
                print(f"📊 两次路径是否相同: {filepath1 == filepath2}")
                
                # 验证文件内容是否被保留
                if os.path.exists(filepath2):
                    with open(filepath2, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    first_paper = data.get('papers', [{}])[0]
                    test_abstract = first_paper.get('abstract', '')
                    test_source = first_paper.get('abstract_source', '')
                    
                    print(f"📊 测试摘要是否保留: {'✅' if '测试摘要' in test_abstract else '❌'}")
                    print(f"📊 测试标记是否保留: {'✅' if test_source == 'test_modification' else '❌'}")
                    
                    if '测试摘要' in test_abstract and test_source == 'test_modification':
                        print("🎉 文件跳过行为正常！已存在的文件没有被覆盖")
                    else:
                        print("⚠️ 文件可能被覆盖了！")
            else:
                print("❌ 第二次调用失败")
                
        except Exception as e:
            print(f"❌ 第二次调用异常: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")


def test_save_papers_to_json_directly():
    """直接测试 save_papers_to_json 方法的跳过行为"""
    
    print("\n🧪 直接测试 save_papers_to_json 方法")
    print("-" * 40)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 临时测试目录: {temp_dir}")
        
        # 创建 DBLPPaperFetcher 实例
        fetcher = DBLPPaperFetcher(data_dir=temp_dir)
        
        # 测试数据
        test_papers = [
            {
                'title': '测试论文1',
                'authors': ['作者1', '作者2'],
                'year': 2021,
                'abstract': '原始摘要内容'
            }
        ]
        
        test_venue = "test_venue"
        test_year = 2021
        
        # 第一次保存
        print("\n📝 第一次保存...")
        filepath1 = fetcher.save_papers_to_json(
            papers=test_papers,
            venue_name=test_venue,
            year=test_year,
            type_counts={'conference': 1},
            venue_counts={'test': 1},
            type_counts_before={'conference': 1},
            venue_counts_before={'test': 1},
            total_papers_before=1
        )
        
        if filepath1:
            print(f"✅ 第一次保存成功: {os.path.basename(filepath1)}")
            
            # 修改文件内容
            with open(filepath1, 'r', encoding='utf-8') as f:
                data = json.load(f)
            data['papers'][0]['abstract'] = '修改后的摘要内容'
            data['papers'][0]['test_marker'] = 'modified'
            
            with open(filepath1, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print("✅ 已修改文件内容")
        
        # 第二次保存 - 应该跳过
        print("\n📝 第二次保存（应该跳过）...")
        filepath2 = fetcher.save_papers_to_json(
            papers=[{'title': '新论文', 'abstract': '新摘要'}],  # 不同的数据
            venue_name=test_venue,
            year=test_year,
            type_counts={'conference': 1},
            venue_counts={'test': 1},
            type_counts_before={'conference': 1},
            venue_counts_before={'test': 1},
            total_papers_before=1
        )
        
        if filepath2:
            print(f"✅ 第二次调用返回路径: {os.path.basename(filepath2)}")
            
            # 验证文件内容是否被保留
            with open(filepath2, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            abstract = data['papers'][0].get('abstract', '')
            test_marker = data['papers'][0].get('test_marker', '')
            
            if '修改后的摘要' in abstract and test_marker == 'modified':
                print("🎉 跳过行为正常！文件内容没有被覆盖")
            else:
                print("⚠️ 文件可能被覆盖了！")
                print(f"摘要内容: {abstract}")
                print(f"测试标记: {test_marker}")


if __name__ == "__main__":
    test_file_skip_behavior()
    test_save_papers_to_json_directly()
