#!/usr/bin/env python3
"""
多数据源摘要获取测试脚本

测试新的多数据源级联获取策略的效果
"""

import os
import sys
import json
import asyncio
import aiohttp
import logging
from typing import List, Dict

# 添加项目根目录到路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(ROOT_DIR)

from crawler.dblp_unify.crawler_dblp_unify import AbstractFetcher


async def test_multi_source_fetching():
    """测试多数据源摘要获取功能"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 测试用的DOI列表 - 选择一些CCFA类会议的论文
    test_dois = [
        # AAAI 2023 论文
        "10.1609/aaai.v37i1.25070",
        "10.1609/aaai.v37i1.25071",
        
        # CAV 2023 论文  
        "10.1007/978-3-031-37706-8_1",
        "10.1007/978-3-031-37706-8_2",
        
        # ICML 2023 论文
        "10.48550/arXiv.2302.13971",
        "10.48550/arXiv.2302.14045",
        
        # CVPR 2023 论文
        "10.1109/CVPR52729.2023.00001",
        "10.1109/CVPR52729.2023.00002",
        
        # SIGMOD 2023 论文
        "10.1145/3588689.3588690",
        "10.1145/3588689.3588691",
    ]
    
    print("🚀 开始测试多数据源摘要获取...")
    print(f"📊 测试DOI数量: {len(test_dois)}")
    print("=" * 60)
    
    # 创建摘要获取器
    fetcher = AbstractFetcher(concurrency=3)  # 降低并发数避免被限制
    
    # 创建aiohttp会话
    connector = aiohttp.TCPConnector(limit=10)
    timeout = aiohttp.ClientTimeout(total=30)
    headers = {'User-Agent': 'Multi-Source-Abstract-Test/1.0'}
    
    results = []
    
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers=headers
    ) as session:
        
        for i, doi in enumerate(test_dois, 1):
            print(f"\n📄 [{i}/{len(test_dois)}] 测试DOI: {doi}")
            
            # 获取摘要
            abstract = await fetcher.fetch_one_abstract(session, doi)
            
            result = {
                'doi': doi,
                'has_abstract': bool(abstract),
                'abstract_length': len(abstract) if abstract else 0,
                'abstract_preview': abstract[:200] + "..." if abstract and len(abstract) > 200 else abstract
            }
            
            results.append(result)
            
            # 输出结果
            if abstract:
                print(f"✅ 成功获取摘要 ({len(abstract)} 字符)")
                print(f"📝 摘要预览: {result['abstract_preview']}")
            else:
                print("❌ 未能获取到摘要")
            
            # 避免请求过快
            await asyncio.sleep(0.5)
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📊 测试结果统计:")
    
    successful_count = sum(1 for r in results if r['has_abstract'])
    success_rate = successful_count / len(results) * 100
    
    print(f"总测试数量: {len(results)}")
    print(f"成功获取: {successful_count}")
    print(f"成功率: {success_rate:.1f}%")
    
    # 按摘要长度分组统计
    length_stats = {}
    for result in results:
        if result['has_abstract']:
            length = result['abstract_length']
            if length < 100:
                category = "短摘要 (<100字符)"
            elif length < 500:
                category = "中等摘要 (100-500字符)"
            else:
                category = "长摘要 (>500字符)"
            
            length_stats[category] = length_stats.get(category, 0) + 1
    
    if length_stats:
        print("\n摘要长度分布:")
        for category, count in length_stats.items():
            print(f"  {category}: {count} 篇")
    
    # 保存详细结果
    output_file = "test_multi_source_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: {output_file}")
    
    return results


async def test_single_doi(doi: str):
    """测试单个DOI的摘要获取"""
    
    print(f"🔍 测试单个DOI: {doi}")
    
    fetcher = AbstractFetcher(concurrency=1)
    
    connector = aiohttp.TCPConnector(limit=5)
    timeout = aiohttp.ClientTimeout(total=30)
    headers = {'User-Agent': 'Single-DOI-Test/1.0'}
    
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers=headers
    ) as session:
        
        abstract = await fetcher.fetch_one_abstract(session, doi)
        
        if abstract:
            print(f"✅ 成功获取摘要 ({len(abstract)} 字符)")
            print(f"📝 完整摘要:\n{abstract}")
        else:
            print("❌ 未能获取到摘要")
        
        return abstract


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='测试多数据源摘要获取功能')
    parser.add_argument('--doi', type=str, help='测试单个DOI')
    parser.add_argument('--batch', action='store_true', help='批量测试多个DOI')
    
    args = parser.parse_args()
    
    if args.doi:
        # 测试单个DOI
        asyncio.run(test_single_doi(args.doi))
    else:
        # 默认批量测试
        asyncio.run(test_multi_source_fetching())
