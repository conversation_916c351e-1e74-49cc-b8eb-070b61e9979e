#!/usr/bin/env python3
"""
测试真正的跳过行为

验证修复后的代码是否真正跳过网络请求
"""

import os
import sys
import json
import tempfile
import logging
import time
from unittest.mock import patch

# 添加项目根目录到路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(ROOT_DIR)

from crawler.dblp_unify.crawler_dblp_unify import DBLPPaperFetcher


def test_real_skip_behavior():
    """测试真正的跳过行为"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🧪 测试真正的跳过行为")
    print("=" * 50)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 临时测试目录: {temp_dir}")
        
        # 创建 DBLPPaperFetcher 实例
        fetcher = DBLPPaperFetcher(data_dir=temp_dir)
        
        # 测试会议和年份
        test_venue = "aaai"
        test_year = 2021
        
        # 预期的文件路径
        expected_filename = f"{test_venue}_{test_year}.json"
        expected_filepath = os.path.join(temp_dir, expected_filename)
        
        print(f"\n📄 测试会议: {test_venue} {test_year}")
        print(f"📄 预期文件: {expected_filename}")
        
        # 第一次调用 - 应该创建新文件
        print("\n🔄 第一次调用 get_papers_by_venue_and_year...")
        start_time = time.time()
        
        try:
            filepath1 = fetcher.get_papers_by_venue_and_year(test_venue, test_year)
            first_call_time = time.time() - start_time
            
            if filepath1:
                print(f"✅ 第一次调用成功，耗时: {first_call_time:.2f}秒")
                print(f"📄 文件路径: {filepath1}")
                
                # 读取文件内容
                if os.path.exists(filepath1):
                    with open(filepath1, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"📊 文件中论文数量: {len(data.get('papers', []))}")
                    
                    # 添加测试标记
                    data['test_marker'] = 'first_call_completed'
                    data['first_call_time'] = first_call_time
                    
                    # 保存修改后的数据
                    with open(filepath1, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    print("✅ 已添加测试标记")
            else:
                print("❌ 第一次调用失败")
                return
                
        except Exception as e:
            print(f"❌ 第一次调用异常: {e}")
            return
        
        # 第二次调用 - 应该立即跳过，不发送网络请求
        print("\n🔄 第二次调用 get_papers_by_venue_and_year...")
        start_time = time.time()
        
        # 使用 mock 来监控是否调用了 fetch_papers 方法
        with patch.object(fetcher, 'fetch_papers') as mock_fetch:
            try:
                filepath2 = fetcher.get_papers_by_venue_and_year(test_venue, test_year)
                second_call_time = time.time() - start_time
                
                if filepath2:
                    print(f"✅ 第二次调用成功，耗时: {second_call_time:.2f}秒")
                    print(f"📊 两次路径是否相同: {filepath1 == filepath2}")
                    print(f"📊 是否调用了 fetch_papers: {'❌ 是' if mock_fetch.called else '✅ 否'}")
                    print(f"⚡ 速度对比: 第二次比第一次快 {first_call_time/second_call_time:.1f} 倍")
                    
                    # 验证文件内容是否被保留
                    if os.path.exists(filepath2):
                        with open(filepath2, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        test_marker = data.get('test_marker', '')
                        original_time = data.get('first_call_time', 0)
                        
                        print(f"📊 测试标记是否保留: {'✅' if test_marker == 'first_call_completed' else '❌'}")
                        print(f"📊 原始数据是否保留: {'✅' if original_time > 0 else '❌'}")
                        
                        if (test_marker == 'first_call_completed' and 
                            not mock_fetch.called and 
                            second_call_time < 1.0):  # 应该很快完成
                            print("🎉 真正的跳过行为正常！")
                            print("   - 没有发送网络请求")
                            print("   - 文件内容没有被覆盖")
                            print("   - 执行速度很快")
                        else:
                            print("⚠️ 跳过行为可能有问题！")
                            if mock_fetch.called:
                                print("   - 仍然发送了网络请求")
                            if second_call_time >= 1.0:
                                print("   - 执行时间过长")
                else:
                    print("❌ 第二次调用失败")
                    
            except Exception as e:
                print(f"❌ 第二次调用异常: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")


def test_performance_comparison():
    """性能对比测试"""
    
    print("\n🚀 性能对比测试")
    print("-" * 40)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        fetcher = DBLPPaperFetcher(data_dir=temp_dir)
        
        # 创建一个假的已存在文件
        test_venue = "test_venue"
        test_year = 2021
        filename = f"{test_venue}_{test_year}.json"
        filepath = os.path.join(temp_dir, filename)
        
        # 创建假文件
        fake_data = {
            'metadata': {
                'venue_name': test_venue,
                'year': test_year,
                'total_papers': 100,
                'fetch_time': '2023-01-01T00:00:00',
                'source': 'DBLP API'
            },
            'papers': [{'title': f'论文{i}', 'abstract': f'摘要{i}'} for i in range(100)]
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(fake_data, f, ensure_ascii=False, indent=2)
        
        print(f"📄 创建了假文件: {filename}")
        
        # 测试跳过性能
        start_time = time.time()
        result_path = fetcher.get_papers_by_venue_and_year(test_venue, test_year)
        skip_time = time.time() - start_time
        
        print(f"⚡ 跳过文件耗时: {skip_time:.4f}秒")
        print(f"📄 返回路径: {os.path.basename(result_path) if result_path else 'None'}")
        
        if skip_time < 0.1:  # 应该在0.1秒内完成
            print("✅ 跳过性能良好")
        else:
            print("⚠️ 跳过性能可能有问题")


if __name__ == "__main__":
    test_real_skip_behavior()
    test_performance_comparison()
